import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/supervisors_controller.dart';
import '../../widgets/layout/responsive_sidebar.dart';
import '../../widgets/supervisors/supervisor_form.dart';
import '../../widgets/common/loading_widget.dart';
import '../../routes/app_routes.dart';
import '../../../core/constants/color_constants.dart';
import '../../../core/utils/responsive_utils.dart';
import '../../../domain/entities/supervisor.dart';

/// Add supervisor page with responsive design
/// Following Single Responsibility Principle by focusing only on adding supervisors
class AddSupervisorPage extends StatelessWidget {
  const AddSupervisorPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).brightness == Brightness.dark 
          ? ColorConstants.backgroundDark 
          : ColorConstants.background,
      body: ResponsiveSidebar(
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    return GetBuilder<SupervisorsController>(
      builder: (controller) {
        return Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              const SizedBox(height: 24),
              Expanded(
                child: _buildForm(context, controller),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildHeader(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final isMobile = deviceType == DeviceScreenType.mobile;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (isMobile) ...[
          // Mobile layout - stacked
          Row(
            children: [
              IconButton(
                onPressed: () => Get.back(),
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'إضافة مشرف جديد',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).brightness == Brightness.dark
                        ? ColorConstants.textPrimaryDark
                        : ColorConstants.textPrimary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'أدخل بيانات المشرف الجديد',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).brightness == Brightness.dark
                  ? ColorConstants.textSecondaryDark
                  : ColorConstants.textSecondary,
            ),
          ),
        ] else ...[
          // Desktop layout - row
          Row(
            children: [
              IconButton(
                onPressed: () => Get.back(),
                icon: Icon(
                  Icons.arrow_back,
                  color: Theme.of(context).brightness == Brightness.dark
                      ? ColorConstants.textPrimaryDark
                      : ColorConstants.textPrimary,
                ),
              ),
              const SizedBox(width: 16),
              Icon(
                Icons.supervisor_account_rounded,
                size: 32,
                color: ColorConstants.primary,
              ),
              const SizedBox(width: 16),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إضافة مشرف جديد',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textPrimaryDark
                          : ColorConstants.textPrimary,
                    ),
                  ),
                  Text(
                    'أدخل بيانات المشرف الجديد',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).brightness == Brightness.dark
                          ? ColorConstants.textSecondaryDark
                          : ColorConstants.textSecondary,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildForm(BuildContext context, SupervisorsController controller) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? ColorConstants.cardDark
            : ColorConstants.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Obx(() {
        if (controller.isLoadingFormData) {
          return const Center(
            child: LoadingWidget(),
          );
        }

        return SupervisorForm(
          supervisor: null, // New supervisor
          availableBuses: controller.availableBuses,
          genderOptions: controller.genderOptions,
          religionOptions: controller.religionOptions,
          bloodTypeOptions: controller.bloodTypeOptions,
          onSubmit: (supervisor) => _handleSubmit(context, controller, supervisor),
          isLoading: controller.isLoading,
        );
      }),
    );
  }

  Future<void> _handleSubmit(
    BuildContext context,
    SupervisorsController controller,
    Supervisor supervisor,
  ) async {
    final success = await controller.createSupervisor(supervisor);
    
    if (success) {
      Get.offNamed(AppRoutes.supervisors);
      Get.snackbar(
        'نجح',
        'تم إنشاء المشرف بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: ColorConstants.success,
        colorText: ColorConstants.white,
      );
    }
  }
}
